# Visual Documentation & Diagrams Index

## Overview

This directory contains comprehensive visual documentation for the Chinook Filament 4 admin panel, including Mermaid v10.6+ ERDs, DBML schema files, and accessibility-compliant diagrams with WCAG 2.1 AA compliance.

## Table of Contents

- [Overview](#overview)
- [Documentation Structure](#documentation-structure)
  - [Database Diagrams](#database-diagrams)
  - [System Architecture](#system-architecture)
- [WCAG 2.1 AA Compliance](#wcag-21-aa-compliance)
- [Diagram Standards](#diagram-standards)
- [Color Palette](#color-palette)
- [Navigation](#navigation)

## Documentation Structure

### Database Diagrams

1. **[Entity Relationship Diagrams](010-entity-relationship-diagrams.md)** - Complete ERD with Mermaid v10.6+ syntax
2. **[Database Schema](020-database-schema.md)** - DBML schema files with annotations

### System Architecture

3. **[System Architecture](050-system-architecture.md)** - Overall system design and components
4. **[Filament Panel Architecture](060-filament-panel-architecture.md)** - Panel structure and organization

## WCAG 2.1 AA Compliance

All visual documentation follows WCAG 2.1 AA accessibility guidelines:

### Color Contrast Requirements

- **Text Contrast**: Minimum 4.5:1 ratio for normal text
- **Large Text Contrast**: Minimum 3:1 ratio for large text (18pt+ or 14pt+ bold)
- **Non-text Contrast**: Minimum 3:1 ratio for UI components and graphics
- **Color Independence**: Information not conveyed by color alone

### Accessibility Features

- **Screen Reader Support**: All diagrams include descriptive alt text
- **Keyboard Navigation**: Interactive elements are keyboard accessible
- **Focus Indicators**: Clear focus indicators for interactive elements
- **Semantic Structure**: Proper heading hierarchy and semantic markup

## Diagram Standards

### Mermaid v10.6+ Compliance

All diagrams use the latest Mermaid syntax and features:

- **Syntax Validation**: Tested with Mermaid CLI and Live Editor
- **Semantic Titles**: Descriptive titles using `---` syntax
- **Accessibility**: High-contrast colors and clear labeling
- **Consistency**: Standardized styling across all diagram types

### Visual Design Principles

- **Clarity**: Clear, unambiguous visual representation
- **Consistency**: Standardized colors, fonts, and styling
- **Accessibility**: WCAG 2.1 AA compliant color schemes
- **Scalability**: Readable at various zoom levels

## Color Palette

### Primary Colors (WCAG 2.1 AA Compliant)

- **Primary Blue**: #1976d2 (Contrast ratio: 7.04:1 on white)
- **Success Green**: #388e3c (Contrast ratio: 6.74:1 on white)
- **Warning Orange**: #f57c00 (Contrast ratio: 4.52:1 on white)
- **Error Red**: #d32f2f (Contrast ratio: 5.25:1 on white)

### Secondary Colors (For backgrounds and light themes)

- **Light Blue**: #e3f2fd (background) with #1976d2 (text)
- **Light Green**: #e8f5e8 (background) with #388e3c (text)
- **Light Orange**: #fff3e0 (background) with #f57c00 (text)
- **Light Red**: #ffebee (background) with #d32f2f (text)

### Usage Guidelines

- **Primary Colors**: Use for main elements and emphasis
- **Secondary Colors**: Use for backgrounds and subtle highlighting
- **Contrast Validation**: All combinations tested for WCAG AA compliance
- **Color Independence**: Never rely solely on color to convey information

## Related Documentation

- **[Filament Setup Guide](../setup/000-index.md)** - Panel configuration and setup
- **[Filament Resources](../resources/000-index.md)** - Resource implementation patterns
- **[Filament Features](../features/000-index.md)** - Advanced features and widgets
- **[Database Documentation](../../000-chinook-index.md)** - Core database implementation

---

## Navigation

**← Previous:** [Filament Documentation Index](../README.md)

**Next →** [Entity Relationship Diagrams](010-entity-relationship-diagrams.md)
