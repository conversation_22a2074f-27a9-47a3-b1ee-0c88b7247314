# Laravel WorkOS Enterprise Authentication Guide

## Table of Contents

- [Overview](#overview)
- [Installation & Configuration](#installation--configuration)
- [SSO Integration Patterns](#sso-integration-patterns)
- [Directory Sync Implementation](#directory-sync-implementation)
- [User Provisioning Workflows](#user-provisioning-workflows)
- [RBAC Integration](#rbac-integration)
- [Multi-Tenant Architecture](#multi-tenant-architecture)
- [Security Best Practices](#security-best-practices)
- [Monitoring & Troubleshooting](#monitoring--troubleshooting)
- [Testing Strategies](#testing-strategies)
- [Production Deployment](#production-deployment)
- [Integration Examples](#integration-examples)

## Overview

Laravel WorkOS provides enterprise-grade Single Sign-On (SSO) and directory synchronization capabilities for Laravel applications. This guide demonstrates how to implement WorkOS with spatie/laravel-permission for comprehensive Role-Based Access Control (RBAC) in enterprise environments.

### Key Features

- **Enterprise SSO**: SAML, OIDC, and OAuth 2.0 support
- **Directory Sync**: Automated user and group synchronization
- **Multi-Tenant Support**: Organization-based access control
- **RBAC Integration**: Seamless permission management
- **Audit Logging**: Comprehensive authentication tracking
- **Security Compliance**: SOC 2, GDPR, and enterprise standards

### Architecture Overview

```mermaid
graph TB
    subgraph "Enterprise Identity Provider"
        A[SAML/OIDC Provider]
        B[Active Directory]
        C[Google Workspace]
    end
    
    subgraph "WorkOS Platform"
        D[WorkOS SSO]
        E[Directory Sync]
        F[Audit Logs]
    end
    
    subgraph "Laravel Application"
        G[WorkOS Middleware]
        H[User Model]
        I[Permission System]
        J[Organization Model]
    end
    
    A --> D
    B --> E
    C --> E
    D --> G
    E --> H
    G --> I
    H --> J
    
    style A fill:#1976d2,color:#fff
    style D fill:#388e3c,color:#fff
    style G fill:#f57c00,color:#fff
    style I fill:#d32f2f,color:#fff
```

## Installation & Configuration

### Package Installation

```bash
# Install WorkOS Laravel package
composer require laravel/workos

# Publish configuration
php artisan vendor:publish --provider="Laravel\WorkOS\WorkOSServiceProvider"

# Run migrations for WorkOS tables
php artisan migrate
```

### Environment Configuration

```env
# WorkOS Configuration
WORKOS_API_KEY=your_workos_api_key
WORKOS_CLIENT_ID=your_workos_client_id
WORKOS_REDIRECT_URI=https://your-app.com/auth/workos/callback

# Organization Configuration
WORKOS_DEFAULT_ORGANIZATION=your_default_org_id
WORKOS_MULTI_TENANT=true

# Directory Sync Configuration
WORKOS_DIRECTORY_SYNC_ENABLED=true
WORKOS_DIRECTORY_SYNC_WEBHOOK_SECRET=your_webhook_secret
```

### Service Provider Configuration

```php
<?php

// config/workos.php
return [
    'api_key' => env('WORKOS_API_KEY'),
    'client_id' => env('WORKOS_CLIENT_ID'),
    'redirect_uri' => env('WORKOS_REDIRECT_URI'),
    
    'organizations' => [
        'default' => env('WORKOS_DEFAULT_ORGANIZATION'),
        'multi_tenant' => env('WORKOS_MULTI_TENANT', false),
    ],
    
    'directory_sync' => [
        'enabled' => env('WORKOS_DIRECTORY_SYNC_ENABLED', false),
        'webhook_secret' => env('WORKOS_DIRECTORY_SYNC_WEBHOOK_SECRET'),
        'auto_provision' => true,
        'auto_deprovision' => false,
    ],
    
    'session' => [
        'lifetime' => 120, // minutes
        'refresh_threshold' => 30, // minutes before expiry
    ],
    
    'cache' => [
        'ttl' => 3600, // seconds
        'prefix' => 'workos',
    ],
];
```

## SSO Integration Patterns

### Authentication Flow Implementation

```php
<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\WorkOSAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\WorkOS\WorkOS;

class WorkOSController extends Controller
{
    public function __construct(
        private WorkOSAuthService $authService,
        private WorkOS $workos
    ) {}

    /**
     * Redirect to WorkOS SSO provider
     */
    public function redirect(Request $request): \Illuminate\Http\RedirectResponse
    {
        $organization = $request->query('organization');
        $provider = $request->query('provider', 'saml');
        
        $authorizationUrl = $this->workos->sso->getAuthorizationUrl([
            'organization' => $organization,
            'provider' => $provider,
            'redirect_uri' => config('workos.redirect_uri'),
            'state' => $this->generateSecureState($request),
        ]);
        
        return redirect($authorizationUrl);
    }

    /**
     * Handle WorkOS callback
     */
    public function callback(Request $request): \Illuminate\Http\RedirectResponse
    {
        try {
            $this->validateState($request);
            
            $profile = $this->workos->sso->getProfile([
                'code' => $request->query('code'),
            ]);
            
            $user = $this->authService->findOrCreateUser($profile);
            $this->authService->syncUserPermissions($user, $profile);
            
            Auth::login($user);
            
            return redirect()->intended('/dashboard');
            
        } catch (\Exception $e) {
            logger()->error('WorkOS authentication failed', [
                'error' => $e->getMessage(),
                'code' => $request->query('code'),
            ]);
            
            return redirect('/login')->withErrors([
                'authentication' => 'Authentication failed. Please try again.'
            ]);
        }
    }

    /**
     * Generate secure state parameter
     */
    private function generateSecureState(Request $request): string
    {
        $state = [
            'csrf' => csrf_token(),
            'timestamp' => time(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ];
        
        return encrypt(json_encode($state));
    }

    /**
     * Validate state parameter
     */
    private function validateState(Request $request): void
    {
        $encryptedState = $request->query('state');
        
        if (!$encryptedState) {
            throw new \InvalidArgumentException('Missing state parameter');
        }
        
        $state = json_decode(decrypt($encryptedState), true);
        
        if (!$state || !isset($state['timestamp'])) {
            throw new \InvalidArgumentException('Invalid state parameter');
        }
        
        // Validate timestamp (5 minute window)
        if (time() - $state['timestamp'] > 300) {
            throw new \InvalidArgumentException('State parameter expired');
        }
        
        // Additional security validations can be added here
    }
}
```

### WorkOS Authentication Service

```php
<?php

namespace App\Services;

use App\Models\User;
use App\Models\Organization;
use Illuminate\Support\Facades\DB;
use Laravel\WorkOS\Entities\Profile;
use Spatie\Permission\Models\Role;

class WorkOSAuthService
{
    /**
     * Find or create user from WorkOS profile
     */
    public function findOrCreateUser(Profile $profile): User
    {
        return DB::transaction(function () use ($profile) {
            $user = User::where('email', $profile->email)->first();
            
            if (!$user) {
                $user = $this->createUserFromProfile($profile);
            } else {
                $user = $this->updateUserFromProfile($user, $profile);
            }
            
            $this->syncOrganizationMembership($user, $profile);
            
            return $user;
        });
    }

    /**
     * Create new user from WorkOS profile
     */
    private function createUserFromProfile(Profile $profile): User
    {
        return User::create([
            'name' => $profile->firstName . ' ' . $profile->lastName,
            'email' => $profile->email,
            'workos_id' => $profile->id,
            'email_verified_at' => now(),
            'organization_id' => $this->getOrganizationId($profile),
        ]);
    }

    /**
     * Update existing user from WorkOS profile
     */
    private function updateUserFromProfile(User $user, Profile $profile): User
    {
        $user->update([
            'name' => $profile->firstName . ' ' . $profile->lastName,
            'workos_id' => $profile->id,
            'organization_id' => $this->getOrganizationId($profile),
            'last_login_at' => now(),
        ]);
        
        return $user;
    }

    /**
     * Get or create organization from profile
     */
    private function getOrganizationId(Profile $profile): ?int
    {
        if (!$profile->organizationId) {
            return null;
        }
        
        $organization = Organization::firstOrCreate(
            ['workos_id' => $profile->organizationId],
            [
                'name' => $profile->organizationName ?? 'Unknown Organization',
                'domain' => $this->extractDomainFromEmail($profile->email),
            ]
        );
        
        return $organization->id;
    }

    /**
     * Extract domain from email address
     */
    private function extractDomainFromEmail(string $email): string
    {
        return substr(strrchr($email, '@'), 1);
    }

    /**
     * Sync user permissions based on WorkOS profile
     */
    public function syncUserPermissions(User $user, Profile $profile): void
    {
        // Remove existing roles
        $user->roles()->detach();
        
        // Assign roles based on WorkOS groups/attributes
        $this->assignRolesFromProfile($user, $profile);
        
        // Log permission changes
        activity()
            ->performedOn($user)
            ->withProperties([
                'workos_id' => $profile->id,
                'organization_id' => $profile->organizationId,
                'roles' => $user->roles->pluck('name')->toArray(),
            ])
            ->log('workos_permissions_synced');
    }

    /**
     * Assign roles based on WorkOS profile attributes
     */
    private function assignRolesFromProfile(User $user, Profile $profile): void
    {
        $roleMapping = config('workos.role_mapping', []);
        
        // Default role assignment
        $defaultRole = Role::findByName('user');
        $user->assignRole($defaultRole);
        
        // Map WorkOS groups to Laravel roles
        if (isset($profile->rawAttributes['groups'])) {
            foreach ($profile->rawAttributes['groups'] as $group) {
                if (isset($roleMapping[$group])) {
                    $role = Role::findByName($roleMapping[$group]);
                    if ($role) {
                        $user->assignRole($role);
                    }
                }
            }
        }
        
        // Organization-specific role assignment
        if ($user->organization && $user->organization->isAdmin($user->email)) {
            $adminRole = Role::findByName('admin');
            $user->assignRole($adminRole);
        }
    }
}
```

## Directory Sync Implementation

### Webhook Handler for Directory Events

```php
<?php

namespace App\Http\Controllers\Webhooks;

use App\Http\Controllers\Controller;
use App\Services\DirectorySyncService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WorkOSWebhookController extends Controller
{
    public function __construct(
        private DirectorySyncService $directorySyncService
    ) {}

    /**
     * Handle WorkOS directory sync webhooks
     */
    public function handle(Request $request): Response
    {
        try {
            $this->validateWebhookSignature($request);

            $payload = $request->json()->all();
            $event = $payload['event'];
            $data = $payload['data'];

            match ($event) {
                'dsync.user.created' => $this->directorySyncService->handleUserCreated($data),
                'dsync.user.updated' => $this->directorySyncService->handleUserUpdated($data),
                'dsync.user.deleted' => $this->directorySyncService->handleUserDeleted($data),
                'dsync.group.created' => $this->directorySyncService->handleGroupCreated($data),
                'dsync.group.updated' => $this->directorySyncService->handleGroupUpdated($data),
                'dsync.group.deleted' => $this->directorySyncService->handleGroupDeleted($data),
                'dsync.group.user_added' => $this->directorySyncService->handleUserAddedToGroup($data),
                'dsync.group.user_removed' => $this->directorySyncService->handleUserRemovedFromGroup($data),
                default => Log::warning('Unknown WorkOS webhook event', ['event' => $event])
            };

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('WorkOS webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->json()->all(),
            ]);

            return response('Error', 500);
        }
    }

    /**
     * Validate webhook signature
     */
    private function validateWebhookSignature(Request $request): void
    {
        $signature = $request->header('WorkOS-Signature');
        $payload = $request->getContent();
        $secret = config('workos.directory_sync.webhook_secret');

        $expectedSignature = hash_hmac('sha256', $payload, $secret);

        if (!hash_equals($expectedSignature, $signature)) {
            throw new \InvalidArgumentException('Invalid webhook signature');
        }
    }
}
```

### Directory Sync Service

```php
<?php

namespace App\Services;

use App\Models\User;
use App\Models\Organization;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class DirectorySyncService
{
    /**
     * Handle user created event
     */
    public function handleUserCreated(array $data): void
    {
        DB::transaction(function () use ($data) {
            $user = User::create([
                'name' => $data['first_name'] . ' ' . $data['last_name'],
                'email' => $data['email'],
                'workos_directory_id' => $data['id'],
                'organization_id' => $this->getOrganizationIdFromDirectory($data['directory_id']),
                'email_verified_at' => now(),
                'is_active' => $data['state'] === 'active',
            ]);

            // Assign default role
            $user->assignRole('user');

            // Log the creation
            activity()
                ->performedOn($user)
                ->withProperties(['directory_data' => $data])
                ->log('directory_sync_user_created');
        });
    }

    /**
     * Handle user updated event
     */
    public function handleUserUpdated(array $data): void
    {
        $user = User::where('workos_directory_id', $data['id'])->first();

        if (!$user) {
            Log::warning('User not found for directory update', ['directory_id' => $data['id']]);
            return;
        }

        DB::transaction(function () use ($user, $data) {
            $user->update([
                'name' => $data['first_name'] . ' ' . $data['last_name'],
                'email' => $data['email'],
                'is_active' => $data['state'] === 'active',
            ]);

            // Handle deactivation
            if ($data['state'] === 'inactive') {
                $user->tokens()->delete(); // Revoke API tokens
                $user->sessions()->delete(); // Invalidate sessions
            }

            activity()
                ->performedOn($user)
                ->withProperties(['directory_data' => $data])
                ->log('directory_sync_user_updated');
        });
    }

    /**
     * Handle user deleted event
     */
    public function handleUserDeleted(array $data): void
    {
        $user = User::where('workos_directory_id', $data['id'])->first();

        if (!$user) {
            return;
        }

        DB::transaction(function () use ($user, $data) {
            // Soft delete or deactivate based on configuration
            if (config('workos.directory_sync.auto_deprovision')) {
                $user->delete();
            } else {
                $user->update(['is_active' => false]);
                $user->tokens()->delete();
                $user->sessions()->delete();
            }

            activity()
                ->performedOn($user)
                ->withProperties(['directory_data' => $data])
                ->log('directory_sync_user_deleted');
        });
    }

    /**
     * Get organization ID from directory ID
     */
    private function getOrganizationIdFromDirectory(string $directoryId): ?int
    {
        $organization = Organization::where('workos_directory_id', $directoryId)->first();

        return $organization?->id;
    }
}
```

## User Provisioning Workflows

### Automated User Provisioning

```php
<?php

namespace App\Services;

use App\Models\User;
use App\Models\Organization;
use App\Notifications\UserProvisionedNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class UserProvisioningService
{
    /**
     * Provision user with organization context
     */
    public function provisionUser(array $userData, Organization $organization): User
    {
        return DB::transaction(function () use ($userData, $organization) {
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'organization_id' => $organization->id,
                'email_verified_at' => now(),
                'is_active' => true,
                'provisioned_at' => now(),
            ]);

            // Apply organization-specific role assignments
            $this->applyOrganizationRoles($user, $organization);

            // Set up user workspace
            $this->setupUserWorkspace($user);

            // Send welcome notification
            $user->notify(new UserProvisionedNotification($organization));

            // Log provisioning activity
            activity()
                ->performedOn($user)
                ->withProperties([
                    'organization_id' => $organization->id,
                    'provisioning_method' => 'workos_directory_sync'
                ])
                ->log('user_provisioned');

            return $user;
        });
    }

    /**
     * Apply organization-specific roles
     */
    private function applyOrganizationRoles(User $user, Organization $organization): void
    {
        // Default role for all users
        $user->assignRole('user');

        // Organization-specific role mapping
        $orgRoleMapping = $organization->getRoleMapping();

        foreach ($orgRoleMapping as $condition => $roleName) {
            if ($this->evaluateRoleCondition($user, $condition)) {
                $user->assignRole($roleName);
            }
        }
    }

    /**
     * Evaluate role assignment conditions
     */
    private function evaluateRoleCondition(User $user, string $condition): bool
    {
        return match ($condition) {
            'domain_admin' => $this->isDomainAdmin($user),
            'department_manager' => $this->isDepartmentManager($user),
            'senior_role' => $this->hasSeniorRole($user),
            default => false
        };
    }

    /**
     * Check if user is domain admin
     */
    private function isDomainAdmin(User $user): bool
    {
        $adminDomains = config('workos.admin_domains', []);
        $userDomain = substr(strrchr($user->email, '@'), 1);

        return in_array($userDomain, $adminDomains);
    }

    /**
     * Setup user workspace
     */
    private function setupUserWorkspace(User $user): void
    {
        // Create user-specific resources
        // This could include creating folders, setting up preferences, etc.

        // Example: Create user profile
        $user->profile()->create([
            'timezone' => $user->organization->default_timezone ?? 'UTC',
            'locale' => $user->organization->default_locale ?? 'en',
            'theme' => 'light',
        ]);
    }
}
```

## RBAC Integration

### Enhanced User Model with WorkOS Integration

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, LogsActivity;

    protected $fillable = [
        'name',
        'email',
        'password',
        'workos_id',
        'workos_directory_id',
        'organization_id',
        'email_verified_at',
        'is_active',
        'last_login_at',
        'provisioned_at',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'provisioned_at' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'is_active', 'organization_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Organization relationship
     */
    public function organization(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Check if user has WorkOS integration
     */
    public function hasWorkOSIntegration(): bool
    {
        return !is_null($this->workos_id);
    }

    /**
     * Check if user is provisioned via directory sync
     */
    public function isDirectorySynced(): bool
    {
        return !is_null($this->workos_directory_id);
    }

    /**
     * Get user's effective permissions within organization context
     */
    public function getOrganizationPermissions(): array
    {
        $permissions = $this->getAllPermissions()->pluck('name')->toArray();

        // Add organization-specific permissions
        if ($this->organization) {
            $orgPermissions = $this->organization->getPermissionsForUser($this);
            $permissions = array_merge($permissions, $orgPermissions);
        }

        return array_unique($permissions);
    }

    /**
     * Check if user can access organization resources
     */
    public function canAccessOrganization(Organization $organization): bool
    {
        // Users can only access their own organization
        if ($this->organization_id !== $organization->id) {
            return false;
        }

        // Check if user is active
        if (!$this->is_active) {
            return false;
        }

        // Additional organization-specific checks
        return $organization->isUserAllowed($this);
    }

    /**
     * Sync roles from WorkOS profile
     */
    public function syncWorkOSRoles(array $workosGroups): void
    {
        $roleMapping = config('workos.group_role_mapping', []);
        $rolesToAssign = [];

        foreach ($workosGroups as $group) {
            if (isset($roleMapping[$group])) {
                $rolesToAssign[] = $roleMapping[$group];
            }
        }

        // Always include default user role
        $rolesToAssign[] = 'user';

        // Sync roles (removes old roles, assigns new ones)
        $this->syncRoles($rolesToAssign);

        // Log role sync
        activity()
            ->performedOn($this)
            ->withProperties([
                'workos_groups' => $workosGroups,
                'assigned_roles' => $rolesToAssign
            ])
            ->log('workos_roles_synced');
    }
}
```

## Multi-Tenant Architecture

### Organization Model with WorkOS Integration

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Organization extends Model
{
    use SoftDeletes, LogsActivity;

    protected $fillable = [
        'name',
        'domain',
        'workos_id',
        'workos_directory_id',
        'settings',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Users relationship
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get organization-specific role mapping
     */
    public function getRoleMapping(): array
    {
        return $this->settings['role_mapping'] ?? [];
    }

    /**
     * Get permissions for a specific user
     */
    public function getPermissionsForUser(User $user): array
    {
        $orgPermissions = $this->settings['permissions'] ?? [];

        // Filter permissions based on user's role within organization
        $userRoles = $user->roles->pluck('name')->toArray();
        $allowedPermissions = [];

        foreach ($orgPermissions as $permission => $requiredRoles) {
            if (array_intersect($userRoles, $requiredRoles)) {
                $allowedPermissions[] = $permission;
            }
        }

        return $allowedPermissions;
    }

    /**
     * Check if user is allowed in this organization
     */
    public function isUserAllowed(User $user): bool
    {
        // Check organization status
        if (!$this->is_active) {
            return false;
        }

        // Check domain restrictions
        if ($this->hasEmailDomainRestrictions()) {
            return $this->isEmailDomainAllowed($user->email);
        }

        return true;
    }

    /**
     * Check if organization has email domain restrictions
     */
    private function hasEmailDomainRestrictions(): bool
    {
        return !empty($this->settings['allowed_domains']);
    }

    /**
     * Check if email domain is allowed
     */
    private function isEmailDomainAllowed(string $email): bool
    {
        $domain = substr(strrchr($email, '@'), 1);
        $allowedDomains = $this->settings['allowed_domains'] ?? [];

        return in_array($domain, $allowedDomains);
    }

    /**
     * Get activity log options
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'domain', 'is_active', 'settings'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
```

### Multi-Tenant Middleware

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EnsureOrganizationAccess
{
    /**
     * Handle an incoming request
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $user = Auth::user();

        if (!$user) {
            return redirect('/login');
        }

        // Check if user has an organization
        if (!$user->organization) {
            return redirect('/organization/setup')
                ->withErrors(['organization' => 'You must be assigned to an organization.']);
        }

        // Check if user can access their organization
        if (!$user->canAccessOrganization($user->organization)) {
            Auth::logout();
            return redirect('/login')
                ->withErrors(['access' => 'Your organization access has been revoked.']);
        }

        // Set organization context for the request
        $request->attributes->set('organization', $user->organization);

        return $next($request);
    }
}
```

## Security Best Practices

### Security Configuration

```php
<?php

// config/workos-security.php
return [
    'session' => [
        'timeout' => env('WORKOS_SESSION_TIMEOUT', 3600), // 1 hour
        'refresh_threshold' => env('WORKOS_REFRESH_THRESHOLD', 300), // 5 minutes
        'max_concurrent_sessions' => env('WORKOS_MAX_SESSIONS', 3),
    ],

    'authentication' => [
        'require_mfa' => env('WORKOS_REQUIRE_MFA', true),
        'allowed_providers' => ['saml', 'oidc', 'oauth2'],
        'force_https' => env('WORKOS_FORCE_HTTPS', true),
    ],

    'audit' => [
        'log_all_events' => env('WORKOS_LOG_ALL_EVENTS', true),
        'retention_days' => env('WORKOS_AUDIT_RETENTION', 90),
        'alert_on_failures' => env('WORKOS_ALERT_FAILURES', true),
    ],

    'rate_limiting' => [
        'login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'webhook_rate_limit' => 100, // per minute
    ],
];
```

### Security Middleware Stack

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class WorkOSSecurityMiddleware
{
    /**
     * Handle an incoming request
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $user = Auth::user();

        if (!$user) {
            return $next($request);
        }

        // Check session validity
        $this->validateSession($user, $request);

        // Check for concurrent sessions
        $this->checkConcurrentSessions($user);

        // Log security events
        $this->logSecurityEvent($user, $request);

        return $next($request);
    }

    /**
     * Validate user session
     */
    private function validateSession(User $user, Request $request): void
    {
        $sessionKey = "workos_session_{$user->id}";
        $sessionData = Cache::get($sessionKey);

        if (!$sessionData) {
            // Session expired or invalid
            Auth::logout();
            abort(401, 'Session expired');
        }

        // Check if session needs refresh
        $refreshThreshold = config('workos-security.session.refresh_threshold');
        if (time() - $sessionData['last_activity'] > $refreshThreshold) {
            $this->refreshUserSession($user);
        }

        // Update last activity
        Cache::put($sessionKey, [
            'last_activity' => time(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ], config('workos-security.session.timeout'));
    }

    /**
     * Check for concurrent sessions
     */
    private function checkConcurrentSessions(User $user): void
    {
        $maxSessions = config('workos-security.session.max_concurrent_sessions');
        $activeSessions = Cache::get("workos_active_sessions_{$user->id}", []);

        if (count($activeSessions) > $maxSessions) {
            // Terminate oldest sessions
            $sessionsToKeep = array_slice($activeSessions, -$maxSessions, null, true);
            Cache::put("workos_active_sessions_{$user->id}", $sessionsToKeep);

            Log::warning('Concurrent session limit exceeded', [
                'user_id' => $user->id,
                'active_sessions' => count($activeSessions),
                'max_allowed' => $maxSessions,
            ]);
        }
    }

    /**
     * Refresh user session with WorkOS
     */
    private function refreshUserSession(User $user): void
    {
        // Implement WorkOS token refresh logic here
        // This would typically involve calling WorkOS API to refresh the session

        activity()
            ->performedOn($user)
            ->log('workos_session_refreshed');
    }

    /**
     * Log security events
     */
    private function logSecurityEvent(User $user, Request $request): void
    {
        if (config('workos-security.audit.log_all_events')) {
            activity()
                ->performedOn($user)
                ->withProperties([
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'route' => $request->route()?->getName(),
                ])
                ->log('workos_access');
        }
    }
}
```

## Monitoring & Troubleshooting

### WorkOS Event Monitoring

```php
<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Notifications\WorkOSAlertNotification;

class WorkOSMonitoringService
{
    /**
     * Monitor authentication events
     */
    public function monitorAuthenticationEvent(string $event, array $data): void
    {
        $metrics = [
            'event' => $event,
            'timestamp' => now(),
            'organization_id' => $data['organization_id'] ?? null,
            'user_id' => $data['user_id'] ?? null,
            'success' => $data['success'] ?? false,
        ];

        // Store metrics
        $this->storeMetrics($metrics);

        // Check for anomalies
        $this->checkForAnomalies($event, $metrics);

        // Update health status
        $this->updateHealthStatus($event, $metrics['success']);
    }

    /**
     * Store authentication metrics
     */
    private function storeMetrics(array $metrics): void
    {
        $key = "workos_metrics_" . date('Y-m-d-H');
        $currentMetrics = Cache::get($key, []);
        $currentMetrics[] = $metrics;

        Cache::put($key, $currentMetrics, 86400); // 24 hours
    }

    /**
     * Check for authentication anomalies
     */
    private function checkForAnomalies(string $event, array $metrics): void
    {
        if ($event === 'authentication_failed') {
            $this->checkFailureRate($metrics);
        }

        if ($event === 'directory_sync_failed') {
            $this->alertDirectorySyncFailure($metrics);
        }
    }

    /**
     * Check authentication failure rate
     */
    private function checkFailureRate(array $metrics): void
    {
        $hourKey = "workos_failures_" . date('Y-m-d-H');
        $failures = Cache::increment($hourKey);
        Cache::expire($hourKey, 3600);

        $threshold = config('workos-security.rate_limiting.failure_threshold', 50);

        if ($failures > $threshold) {
            Log::critical('High WorkOS authentication failure rate detected', [
                'failures_per_hour' => $failures,
                'threshold' => $threshold,
            ]);

            // Send alert notification
            $this->sendAlert('high_failure_rate', [
                'failures' => $failures,
                'threshold' => $threshold,
            ]);
        }
    }

    /**
     * Send monitoring alert
     */
    private function sendAlert(string $type, array $data): void
    {
        $adminUsers = User::role('super-admin')->get();

        foreach ($adminUsers as $admin) {
            $admin->notify(new WorkOSAlertNotification($type, $data));
        }
    }
}
```

### Troubleshooting Common Issues

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WorkOSDiagnosticsService;

class WorkOSDiagnosticsCommand extends Command
{
    protected $signature = 'workos:diagnose {--fix : Attempt to fix detected issues}';
    protected $description = 'Diagnose WorkOS integration issues';

    public function handle(WorkOSDiagnosticsService $diagnostics): int
    {
        $this->info('Running WorkOS diagnostics...');

        $issues = $diagnostics->runDiagnostics();

        if (empty($issues)) {
            $this->info('✅ No issues detected');
            return 0;
        }

        $this->error('❌ Issues detected:');

        foreach ($issues as $issue) {
            $this->line("  • {$issue['description']}");

            if ($this->option('fix') && $issue['fixable']) {
                $this->line("    Attempting to fix...");
                $result = $diagnostics->fixIssue($issue['type']);

                if ($result) {
                    $this->info("    ✅ Fixed");
                } else {
                    $this->error("    ❌ Fix failed");
                }
            }
        }

        return count($issues);
    }
}
```

## Testing Strategies

### WorkOS Integration Tests

```php
<?php

namespace Tests\Feature\WorkOS;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\WorkOS\Facades\WorkOS;

class WorkOSAuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_workos_callback_creates_new_user(): void
    {
        // Mock WorkOS profile response
        WorkOS::fake([
            'sso.getProfile' => [
                'id' => 'workos_user_123',
                'email' => '<EMAIL>',
                'firstName' => 'John',
                'lastName' => 'Doe',
                'organizationId' => 'org_123',
                'organizationName' => 'Example Corp',
            ]
        ]);

        $response = $this->get('/auth/workos/callback?code=test_code&state=test_state');

        $response->assertRedirect('/dashboard');

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'workos_id' => 'workos_user_123',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->hasRole('user'));
    }

    public function test_directory_sync_webhook_creates_user(): void
    {
        $organization = Organization::factory()->create([
            'workos_directory_id' => 'dir_123'
        ]);

        $payload = [
            'event' => 'dsync.user.created',
            'data' => [
                'id' => 'dir_user_123',
                'email' => '<EMAIL>',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'directory_id' => 'dir_123',
                'state' => 'active',
            ]
        ];

        $response = $this->postJson('/webhooks/workos', $payload, [
            'WorkOS-Signature' => $this->generateWebhookSignature($payload)
        ]);

        $response->assertOk();

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'workos_directory_id' => 'dir_user_123',
            'organization_id' => $organization->id,
        ]);
    }

    private function generateWebhookSignature(array $payload): string
    {
        $secret = config('workos.directory_sync.webhook_secret');
        return hash_hmac('sha256', json_encode($payload), $secret);
    }
}
```

## Production Deployment

### Environment Configuration Checklist

```bash
# Production Environment Variables
WORKOS_API_KEY=prod_api_key_here
WORKOS_CLIENT_ID=prod_client_id_here
WORKOS_REDIRECT_URI=https://yourapp.com/auth/workos/callback

# Security Settings
WORKOS_REQUIRE_MFA=true
WORKOS_FORCE_HTTPS=true
WORKOS_SESSION_TIMEOUT=3600
WORKOS_MAX_SESSIONS=3

# Monitoring
WORKOS_LOG_ALL_EVENTS=true
WORKOS_AUDIT_RETENTION=90
WORKOS_ALERT_FAILURES=true

# Directory Sync
WORKOS_DIRECTORY_SYNC_ENABLED=true
WORKOS_DIRECTORY_SYNC_WEBHOOK_SECRET=your_webhook_secret_here
```

### Deployment Verification Script

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Laravel\WorkOS\WorkOS;

class VerifyWorkOSDeploymentCommand extends Command
{
    protected $signature = 'workos:verify-deployment';
    protected $description = 'Verify WorkOS production deployment';

    public function handle(): int
    {
        $this->info('Verifying WorkOS deployment...');

        $checks = [
            'API Connection' => $this->checkApiConnection(),
            'Configuration' => $this->checkConfiguration(),
            'Database Tables' => $this->checkDatabaseTables(),
            'Webhook Endpoint' => $this->checkWebhookEndpoint(),
            'Security Settings' => $this->checkSecuritySettings(),
        ];

        $allPassed = true;

        foreach ($checks as $check => $passed) {
            $status = $passed ? '✅' : '❌';
            $this->line("{$status} {$check}");

            if (!$passed) {
                $allPassed = false;
            }
        }

        if ($allPassed) {
            $this->info('🎉 All checks passed! WorkOS is ready for production.');
            return 0;
        } else {
            $this->error('❌ Some checks failed. Please review the configuration.');
            return 1;
        }
    }

    private function checkApiConnection(): bool
    {
        try {
            $workos = app(WorkOS::class);
            // Attempt a simple API call
            $workos->organizations->list(['limit' => 1]);
            return true;
        } catch (\Exception $e) {
            $this->error("API connection failed: {$e->getMessage()}");
            return false;
        }
    }

    private function checkConfiguration(): bool
    {
        $required = [
            'WORKOS_API_KEY',
            'WORKOS_CLIENT_ID',
            'WORKOS_REDIRECT_URI',
        ];

        foreach ($required as $key) {
            if (empty(env($key))) {
                $this->error("Missing required environment variable: {$key}");
                return false;
            }
        }

        return true;
    }

    private function checkDatabaseTables(): bool
    {
        try {
            \DB::table('users')->limit(1)->get();
            \DB::table('organizations')->limit(1)->get();
            return true;
        } catch (\Exception $e) {
            $this->error("Database check failed: {$e->getMessage()}");
            return false;
        }
    }

    private function checkWebhookEndpoint(): bool
    {
        $url = config('app.url') . '/webhooks/workos';

        try {
            $response = \Http::timeout(10)->get($url);
            return $response->status() === 405; // Method not allowed is expected for GET
        } catch (\Exception $e) {
            $this->error("Webhook endpoint check failed: {$e->getMessage()}");
            return false;
        }
    }

    private function checkSecuritySettings(): bool
    {
        $securityChecks = [
            'HTTPS enforced' => config('workos-security.authentication.force_https'),
            'MFA required' => config('workos-security.authentication.require_mfa'),
            'Audit logging enabled' => config('workos-security.audit.log_all_events'),
        ];

        foreach ($securityChecks as $check => $enabled) {
            if (!$enabled) {
                $this->warn("Security setting not enabled: {$check}");
                return false;
            }
        }

        return true;
    }
}
```

## Integration Examples

### Complete WorkOS + RBAC Implementation

This guide provides a comprehensive implementation of Laravel WorkOS with spatie/laravel-permission for enterprise-grade authentication and authorization. The integration supports:

- **Single Sign-On (SSO)** with SAML, OIDC, and OAuth 2.0 providers
- **Directory Synchronization** for automated user and group management
- **Multi-Tenant Architecture** with organization-based access control
- **Role-Based Access Control (RBAC)** with granular permissions
- **Security Best Practices** including session management and audit logging
- **Production-Ready Deployment** with comprehensive monitoring and troubleshooting

For additional examples and advanced configurations, refer to the [WorkOS Laravel documentation](https://workos.com/docs/integrations/laravel) and the [spatie/laravel-permission documentation](https://spatie.be/docs/laravel-permission).

---

**Next Steps:**

- Review the [Spatie Laravel Query Builder Guide](101-laravel-query-builder-guide.md) for API development patterns
- Explore [Spatie Comments System Guide](111-spatie-comments-guide.md) for user engagement features
- Check the [Laravel Folio Guide](121-laravel-folio-guide.md) for modern routing patterns
