# Spatie Laravel Settings Guide

## Table of Contents

- [Overview](#overview)
- [Installation & Configuration](#installation--configuration)
- [Basic Usage](#basic-usage)
- [Advanced Features](#advanced-features)
- [Integration with Chinook](#integration-with-chinook)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)
- [Navigation](#navigation)

## Overview

The Spatie Laravel Settings package provides a powerful and flexible way to manage application settings in Laravel applications. This guide demonstrates how to integrate and use this package within the Chinook music database system for managing system-wide configuration options.

**Key Features:**
- **Type-Safe Settings**: Strongly typed setting classes with validation
- **Database Storage**: Persistent settings storage with caching support
- **Encryption Support**: Automatic encryption for sensitive settings
- **Migration Support**: Version-controlled settings with migration capabilities
- **Laravel 12 Compatibility**: Full support for modern Laravel 12 patterns

## Installation & Configuration

### Package Installation

Install the package using Composer:

```bash
composer require spatie/laravel-settings
```

### Publish Configuration

Publish the configuration file and migrations:

```bash
php artisan vendor:publish --provider="Spatie\LaravelSettings\LaravelSettingsServiceProvider" --tag="migrations"
php artisan vendor:publish --provider="Spatie\LaravelSettings\LaravelSettingsServiceProvider" --tag="config"
```

### Run Migrations

Execute the migrations to create the settings table:

```bash
php artisan migrate
```

### Configuration File

Configure the package in `config/settings.php`:

```php
<?php

return [
    /*
     * The settings will be stored in this table.
     */
    'table' => 'settings',

    /*
     * Settings will be cached in this store.
     */
    'cache_store' => env('SETTINGS_CACHE_STORE', 'default'),

    /*
     * The cache key prefix for settings.
     */
    'cache_prefix' => env('SETTINGS_CACHE_PREFIX', 'spatie.laravel-settings.'),

    /*
     * Settings classes will be automatically discovered in these paths.
     */
    'auto_discover_settings' => [
        app_path('Settings'),
    ],

    /*
     * Automatically register settings classes.
     */
    'auto_register' => true,

    /*
     * Settings migrations will be stored in this path.
     */
    'migrations_path' => database_path('settings'),
];
```

## Basic Usage

### Creating Settings Classes

Create a settings class for Chinook system configuration:

```php
<?php

namespace App\Settings;

use Spatie\LaravelSettings\Settings;

class ChinookSettings extends Settings
{
    public string $app_name;
    public string $app_description;
    public bool $registration_enabled;
    public int $max_playlist_tracks;
    public float $default_track_price;
    public array $supported_audio_formats;
    public bool $enable_recommendations;
    
    public static function group(): string
    {
        return 'chinook';
    }
    
    public static function cast(): array
    {
        return [
            'supported_audio_formats' => 'array',
            'registration_enabled' => 'boolean',
            'enable_recommendations' => 'boolean',
            'max_playlist_tracks' => 'integer',
            'default_track_price' => 'float',
        ];
    }
}
```

### Using Settings in Controllers

Access settings in your Filament resources and controllers:

```php
<?php

namespace App\Filament\Resources;

use App\Settings\ChinookSettings;
use Filament\Resources\Resource;

class TrackResource extends Resource
{
    public static function form(Form $form): Form
    {
        $settings = app(ChinookSettings::class);
        
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                    
                TextInput::make('unit_price')
                    ->numeric()
                    ->default($settings->default_track_price)
                    ->prefix('$')
                    ->step(0.01),
                    
                Select::make('media_type_id')
                    ->relationship('mediaType', 'name')
                    ->options(function () use ($settings) {
                        return MediaType::whereIn('extension', $settings->supported_audio_formats)
                            ->pluck('name', 'id');
                    }),
            ]);
    }
}
```

### Settings Migrations

Create settings migrations for version control:

```bash
php artisan make:settings-migration CreateChinookSettings
```

```php
<?php

use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('chinook.app_name', 'Chinook Music Store');
        $this->migrator->add('chinook.app_description', 'Digital music store and streaming platform');
        $this->migrator->add('chinook.registration_enabled', true);
        $this->migrator->add('chinook.max_playlist_tracks', 1000);
        $this->migrator->add('chinook.default_track_price', 0.99);
        $this->migrator->add('chinook.supported_audio_formats', ['mp3', 'flac', 'wav', 'aac']);
        $this->migrator->add('chinook.enable_recommendations', true);
    }
    
    public function down(): void
    {
        $this->migrator->delete('chinook.app_name');
        $this->migrator->delete('chinook.app_description');
        $this->migrator->delete('chinook.registration_enabled');
        $this->migrator->delete('chinook.max_playlist_tracks');
        $this->migrator->delete('chinook.default_track_price');
        $this->migrator->delete('chinook.supported_audio_formats');
        $this->migrator->delete('chinook.enable_recommendations');
    }
};
```

## Advanced Features

### Encrypted Settings

For sensitive configuration data:

```php
<?php

namespace App\Settings;

use Spatie\LaravelSettings\Settings;

class ChinookSecuritySettings extends Settings
{
    public string $api_key;
    public string $webhook_secret;
    public array $payment_gateway_credentials;
    
    public static function group(): string
    {
        return 'chinook_security';
    }
    
    public static function encrypted(): array
    {
        return [
            'api_key',
            'webhook_secret',
            'payment_gateway_credentials',
        ];
    }
}
```

### Settings Validation

Add validation rules to settings classes:

```php
<?php

namespace App\Settings;

use Spatie\LaravelSettings\Settings;

class ChinookSettings extends Settings
{
    public string $app_name;
    public int $max_playlist_tracks;
    public float $default_track_price;
    
    public static function rules(): array
    {
        return [
            'app_name' => ['required', 'string', 'max:255'],
            'max_playlist_tracks' => ['required', 'integer', 'min:1', 'max:10000'],
            'default_track_price' => ['required', 'numeric', 'min:0', 'max:999.99'],
        ];
    }
    
    public static function group(): string
    {
        return 'chinook';
    }
}
```

### Filament Settings Page

Create a Filament page for managing settings:

```php
<?php

namespace App\Filament\Pages;

use App\Settings\ChinookSettings;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class ManageChinookSettings extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'System';
    protected static ?string $title = 'Chinook Settings';
    
    protected static string $settings = ChinookSettings::class;
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Application Settings')
                    ->schema([
                        TextInput::make('app_name')
                            ->label('Application Name')
                            ->required()
                            ->maxLength(255),
                            
                        TextInput::make('app_description')
                            ->label('Application Description')
                            ->maxLength(500),
                    ]),
                    
                Section::make('Feature Settings')
                    ->schema([
                        Toggle::make('registration_enabled')
                            ->label('Enable User Registration'),
                            
                        Toggle::make('enable_recommendations')
                            ->label('Enable Music Recommendations'),
                    ]),
                    
                Section::make('Business Settings')
                    ->schema([
                        TextInput::make('max_playlist_tracks')
                            ->label('Maximum Tracks per Playlist')
                            ->numeric()
                            ->required()
                            ->minValue(1)
                            ->maxValue(10000),
                            
                        TextInput::make('default_track_price')
                            ->label('Default Track Price')
                            ->numeric()
                            ->prefix('$')
                            ->step(0.01)
                            ->required(),
                            
                        TagsInput::make('supported_audio_formats')
                            ->label('Supported Audio Formats')
                            ->placeholder('Add format (e.g., mp3)')
                            ->required(),
                    ]),
            ]);
    }
}
```

## Integration with Chinook

### Track Pricing Integration

Use settings for dynamic track pricing:

```php
<?php

namespace App\Models;

use App\Settings\ChinookSettings;
use Illuminate\Database\Eloquent\Model;

class Track extends Model
{
    protected $fillable = [
        'name', 'album_id', 'media_type_id', 'composer_id',
        'milliseconds', 'bytes', 'unit_price'
    ];
    
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($track) {
            if (empty($track->unit_price)) {
                $settings = app(ChinookSettings::class);
                $track->unit_price = $settings->default_track_price;
            }
        });
    }
    
    public function cast(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'milliseconds' => 'integer',
            'bytes' => 'integer',
        ];
    }
}
```

### Playlist Validation

Enforce playlist limits using settings:

```php
<?php

namespace App\Http\Requests;

use App\Settings\ChinookSettings;
use Illuminate\Foundation\Http\FormRequest;

class AddTrackToPlaylistRequest extends FormRequest
{
    public function rules(): array
    {
        $settings = app(ChinookSettings::class);
        
        return [
            'playlist_id' => ['required', 'exists:playlists,id'],
            'track_id' => ['required', 'exists:tracks,id'],
            'playlist_tracks_count' => [
                'max:' . $settings->max_playlist_tracks
            ],
        ];
    }
    
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $settings = app(ChinookSettings::class);
            $playlist = Playlist::find($this->playlist_id);
            
            if ($playlist && $playlist->tracks()->count() >= $settings->max_playlist_tracks) {
                $validator->errors()->add(
                    'playlist_id',
                    "Playlist cannot exceed {$settings->max_playlist_tracks} tracks."
                );
            }
        });
    }
}
```

## Testing

### Settings Testing

Test settings functionality with Pest:

```php
<?php

use App\Settings\ChinookSettings;

describe('Chinook Settings', function () {
    it('can store and retrieve settings', function () {
        $settings = app(ChinookSettings::class);
        
        $settings->app_name = 'Test Chinook';
        $settings->max_playlist_tracks = 500;
        $settings->save();
        
        $freshSettings = app(ChinookSettings::class);
        
        expect($freshSettings->app_name)->toBe('Test Chinook');
        expect($freshSettings->max_playlist_tracks)->toBe(500);
    });
    
    it('validates settings data', function () {
        $settings = app(ChinookSettings::class);
        
        expect(fn() => $settings->fill(['max_playlist_tracks' => -1]))
            ->toThrow(ValidationException::class);
    });
    
    it('applies default track pricing', function () {
        $settings = app(ChinookSettings::class);
        $settings->default_track_price = 1.29;
        $settings->save();
        
        $track = Track::factory()->create(['unit_price' => null]);
        
        expect($track->unit_price)->toBe(1.29);
    });
});
```

## Troubleshooting

### Common Issues

**Settings Not Persisting**
- Verify database connection and migrations
- Check cache configuration and clear cache if needed
- Ensure settings class is properly registered

**Validation Errors**
- Review validation rules in settings class
- Check data types and casting configuration
- Verify required fields are provided

**Performance Issues**
- Enable caching in configuration
- Use appropriate cache store (Redis recommended)
- Consider settings grouping for large applications

### Debug Commands

```bash
# Clear settings cache
php artisan cache:clear

# List all registered settings
php artisan settings:discover

# Run settings migrations
php artisan settings:migrate
```

---

## Navigation

**← Previous:** [NNJeim World Guide](121-laravel-folio-guide.md)

**Next →** [Spatie Laravel Query Builder Guide](140-spatie-laravel-query-builder-guide.md)

**↑ Back to:** [Package Index](000-packages-index.md)
